蛋白组差异分析结果使用指南
================================

📋 快速开始
----------
1. 首先阅读 README.md 了解整体情况
2. 查看 "最终蛋白组差异分析报告.md" 获取详细分析结果
3. 浏览 figures/ 文件夹中的图表
4. 查看 tables/ 文件夹中的数据表格

📊 主要文件说明
--------------

📄 报告文件：
- README.md - 项目总览和使用说明
- 最终蛋白组差异分析报告.md - 完整分析报告
- 使用指南.txt - 本文件

📈 图表文件 (figures/)：
- final_volcano_plot.png - 火山图，展示所有蛋白的差异表达情况
- final_*_GO_*_bubble.png - GO功能富集气泡图
- final_*_KEGG_bubble.png - KEGG通路富集气泡图

🕸️ 网络图 (networks/)：
- final_*_BP_network.png - 生物过程网络互作图
- final_*_KEGG_network.png - KEGG通路网络互作图

📊 数据表格 (tables/)：
- final_enrichment_results.xlsx - 包含所有分析结果的Excel文件

🐍 分析脚本：
- final_analysis.py - 最终完整分析脚本
- improved_analysis.py - 改进版分析脚本
- quick_analysis.py - 快速分析脚本
- protein_analysis.py - 基础分析脚本

🎯 关键发现
----------
✅ 差异蛋白总数：627个 (占总蛋白的14.2%)
✅ 上调蛋白：141个
✅ 下调蛋白：486个

🔍 主要特征：
- 下调蛋白数量显著多于上调蛋白
- 提示HFsnEF组相比HFnEF组整体蛋白表达水平下降

📋 上调蛋白主要功能：
1. 蛋白水解 (proteolysis)
2. 有氧呼吸 (aerobic respiration)  
3. 线粒体ATP合成
4. 线粒体电子传递
5. 先天免疫反应

📋 下调蛋白主要功能：
1. 蛋白质结合 (identical protein binding)
2. RNA结合 (RNA binding)
3. ATP结合 (ATP binding)
4. 钙离子结合 (calcium ion binding)
5. 金属离子结合 (metal ion binding)

🛣️ 主要通路变化：
上调通路：
- 中性粒细胞胞外陷阱形成
- 系统性红斑狼疮
- 糖尿病心肌病
- 癌症转录失调

下调通路：
- 新冠病毒感染 (COVID-19)
- 补体和凝血级联反应
- 核糖体功能
- 运动蛋白

📖 图表解读指南
--------------

🌋 火山图 (Volcano Plot)：
- 横轴：log2(倍数变化) - 正值表示上调，负值表示下调
- 纵轴：-log10(P值) - 值越大表示越显著
- 红点：上调蛋白
- 蓝点：下调蛋白
- 灰点：无显著差异

🫧 气泡图 (Bubble Plot)：
- 气泡大小：富集的蛋白数量
- 气泡颜色：统计显著性 (越红越显著)
- 横轴：富集比例
- 纵轴：功能条目或通路名称

🕸️ 网络图 (Network Plot)：
- 节点大小：涉及的蛋白数量
- 节点颜色：蛋白数量深浅
- 连线：功能或通路间的关系

💡 使用建议
----------

🔬 科研用途：
1. 引用火山图展示差异蛋白分布
2. 使用气泡图展示功能富集结果
3. 利用网络图展示功能间关系
4. 参考Excel表格获取具体数据

📊 数据挖掘：
1. 关注高倍数变化的蛋白
2. 分析显著富集的通路
3. 探索网络中的关键节点
4. 结合文献验证生物学意义

📈 进一步分析：
1. 可以基于关键蛋白进行验证实验
2. 深入研究重要通路的调控机制
3. 结合临床数据进行关联分析
4. 探索潜在的治疗靶点

⚠️ 注意事项
-----------
1. 本分析基于现有GO和KEGG注释，可能存在注释不完整的情况
2. 网络图中的连接关系是基于功能相似性的简化模型
3. 统计显著性的计算采用了模拟方法，实际应用中建议使用专业统计软件
4. 生物学解释需要结合具体的实验背景和文献资料

🔄 重新运行分析
--------------
如需重新运行或修改分析参数：

1. 确保Python环境已安装必要包：
   - pandas
   - numpy
   - matplotlib
   - seaborn
   - networkx

2. 运行分析脚本：
   python final_analysis.py

3. 结果将保存在当前文件夹中

📞 技术支持
----------
如有任何问题或需要进一步的分析，请联系分析团队。

分析完成时间：2025年08月02日
分析工具：Python + pandas + matplotlib + networkx
