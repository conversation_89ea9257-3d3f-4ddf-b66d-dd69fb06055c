# 蛋白组分析图表样式调整指南

## 📋 概述

本指南详细说明如何修改气泡图和网络图的样式参数，所有可调整的参数都已在代码中明确标注。

## 🎨 气泡图样式调整

### 📍 代码位置
文件：`style_improved_analysis.py`  
函数：`create_improved_bubble_plot()`  
标注区域：`# ========== 气泡图样式参数设置区域 START/END ==========`

### 🔧 可调整参数

#### 1. 图形尺寸设置
```python
fig_width = 10                    # 图形宽度
fig_height = max(6, len(terms) * 0.25)  # 图形高度（自适应）
```

#### 2. 气泡大小设置
```python
min_bubble_size = 20      # 最小气泡大小
max_bubble_size = 120     # 最大气泡大小  
size_multiplier = 20      # 大小倍数（控制整体气泡大小）
```

**调整建议**：
- 气泡太大：减小 `size_multiplier` 和 `max_bubble_size`
- 气泡太小：增大 `size_multiplier` 和 `min_bubble_size`

#### 3. 颜色方案设置
```python
color_map = 'OrRd'        # 颜色映射
bubble_alpha = 0.8        # 气泡透明度 (0-1)
edge_color = 'darkred'    # 边框颜色
edge_width = 1.0          # 边框宽度
```

**可选颜色方案**：
- `'OrRd'` - 橙红渐变（当前使用）
- `'Reds'` - 红色渐变
- `'YlOrRd'` - 黄橙红渐变
- `'plasma'` - 紫红渐变
- `'viridis'` - 蓝绿渐变

#### 4. 背景和网格设置
```python
background_color = '#f8f8f8'  # 背景颜色
grid_alpha = 0.3              # 网格透明度
```

### 📊 标签和轴设置

#### X轴和Y轴标签
```python
ax.set_xlabel('Ratio', fontsize=12, fontweight='bold')
ax.set_ylabel('GO BiologicalProcess enrichment', fontsize=12, fontweight='bold')
```

#### 条目标签长度
```python
short_terms = [term[:45] + '...' if len(term) > 45 else term for term in terms]
```
修改 `45` 来调整标签显示长度。

## 🕸️ 网络图样式调整

### 📍 代码位置
文件：`style_improved_analysis.py`  
函数：`create_improved_network_plot()`  
标注区域：`# ========== 网络图样式参数设置区域 START/END ==========`

### 🔧 可调整参数

#### 1. 图形尺寸设置
```python
fig_width = 14            # 图形宽度
fig_height = 10           # 图形高度
```

#### 2. 节点样式设置
```python
node_size_multiplier = 120    # 节点大小倍数
color_map = 'RdBu_r'         # 颜色映射（红蓝渐变）
node_alpha = 0.85            # 节点透明度
node_edge_color = 'black'    # 节点边框颜色
node_edge_width = 1.5        # 节点边框宽度
```

**可选颜色方案**：
- `'RdBu_r'` - 红蓝渐变（当前使用）
- `'coolwarm'` - 冷暖色渐变
- `'seismic'` - 地震色谱
- `'RdYlBu_r'` - 红黄蓝渐变

#### 3. 边样式设置
```python
edge_alpha = 0.4             # 边透明度
edge_width = 1.5             # 边宽度
edge_color = 'lightgray'     # 边颜色
```

#### 4. 标签样式设置
```python
label_font_size = 11         # 标签字体大小
label_font_color = 'white'   # 标签字体颜色
label_font_weight = 'bold'   # 标签字体粗细
```

**字体颜色选项**：
- `'white'` - 白色（当前使用）
- `'black'` - 黑色
- `'navy'` - 深蓝色
- `'darkred'` - 深红色

#### 5. 布局参数设置
```python
layout_k = 3.5               # 节点间距（越大间距越大）
layout_iterations = 60       # 布局迭代次数（越大布局越稳定）
```

#### 6. 节点标签长度
```python
short_label = term[:20] + '...' if len(term) > 20 else term
```
修改 `20` 来调整节点标签显示长度。

## 🎯 快速调整建议

### 气泡图常见调整

#### 问题：气泡太大
```python
size_multiplier = 15      # 从20改为15
max_bubble_size = 100     # 从120改为100
```

#### 问题：颜色太淡
```python
bubble_alpha = 0.9        # 从0.8改为0.9
edge_width = 1.5          # 从1.0改为1.5
```

#### 问题：背景太灰
```python
background_color = 'white'  # 从'#f8f8f8'改为'white'
```

### 网络图常见调整

#### 问题：节点太密集
```python
layout_k = 4.0            # 从3.5改为4.0
fig_width = 16            # 从14改为16
```

#### 问题：标签看不清
```python
label_font_size = 13      # 从11改为13
label_font_color = 'black'  # 从'white'改为'black'
```

#### 问题：节点太小
```python
node_size_multiplier = 150  # 从120改为150
```

## 🔄 修改流程

1. **找到对应的参数设置区域**
   - 气泡图：搜索 `气泡图样式参数设置区域`
   - 网络图：搜索 `网络图样式参数设置区域`

2. **修改参数值**
   - 根据上述指南修改相应参数

3. **重新运行脚本**
   ```bash
   python protein_analysis_results/style_improved_analysis.py
   ```

4. **查看结果**
   - 气泡图：`figures_improved/` 文件夹
   - 网络图：`networks_improved/` 文件夹

## 📝 参数对照表

| 效果 | 气泡图参数 | 网络图参数 |
|------|------------|------------|
| 增大元素 | `size_multiplier` ↑ | `node_size_multiplier` ↑ |
| 调整透明度 | `bubble_alpha` | `node_alpha` |
| 改变颜色 | `color_map` | `color_map` |
| 调整间距 | `fig_height` | `layout_k` |
| 字体大小 | `fontsize` | `label_font_size` |

## ⚠️ 注意事项

1. **参数范围**：
   - 透明度：0-1之间
   - 字体大小：建议8-16
   - 节点间距：建议2-6

2. **颜色兼容性**：
   - 确保选择的颜色映射存在于matplotlib中
   - 建议先测试小数据集

3. **性能考虑**：
   - 布局迭代次数过高会影响生成速度
   - 节点数量过多时适当减小字体

4. **文件保存**：
   - 修改后的图片会保存到 `figures_improved/` 和 `networks_improved/` 文件夹
   - 不会覆盖原始结果

## 🎨 推荐配色方案

### 学术风格
```python
# 气泡图
color_map = 'Blues'
edge_color = 'navy'
background_color = 'white'

# 网络图  
color_map = 'viridis'
label_font_color = 'white'
```

### 生物医学风格
```python
# 气泡图
color_map = 'OrRd'
edge_color = 'darkred'
background_color = '#f8f8f8'

# 网络图
color_map = 'RdBu_r'
label_font_color = 'white'
```

### 高对比度风格
```python
# 气泡图
color_map = 'plasma'
edge_color = 'black'
background_color = 'white'

# 网络图
color_map = 'coolwarm'
label_font_color = 'black'
```

---

**提示**：所有参数都可以独立调整，建议逐步修改并查看效果，找到最适合您需求的样式配置。
