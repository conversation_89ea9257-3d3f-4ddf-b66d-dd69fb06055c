# 蛋白组差异分析结果说明

## 📊 分析概况

本项目对HFnEF vs HFsnEF蛋白组数据进行了全面的差异分析，包括：
- **差异蛋白识别**
- **GO功能富集分析**
- **KEGG通路富集分析**
- **网络互作分析**
- **可视化展示**

## 📈 主要发现

### 数据统计
- **总蛋白数**: 4,401个
- **差异蛋白数**: 627个 (14.2%)
- **上调蛋白**: 141个
- **下调蛋白**: 486个

### 关键结果
1. **下调蛋白数量显著多于上调蛋白**，提示HFsnEF组相比HFnEF组整体蛋白表达水平下降
2. **上调蛋白主要涉及**：
   - 蛋白水解 (proteolysis)
   - 有氧呼吸 (aerobic respiration)
   - 线粒体ATP合成
   - 先天免疫反应
3. **下调蛋白主要涉及**：
   - 蛋白质结合
   - RNA结合
   - 补体激活和凝血级联反应
   - 新冠病毒感染相关通路

## 📁 文件结构

```
protein_analysis_results/
├── 📋 README.md                           # 本说明文件
├── 📄 最终蛋白组差异分析报告.md            # 完整分析报告
├── 📊 figures/                            # 可视化图表
│   ├── final_volcano_plot.png             # 火山图
│   ├── final_up_proteins_GO_BP_bubble.png # 上调蛋白BP富集气泡图
│   ├── final_up_proteins_GO_CC_bubble.png # 上调蛋白CC富集气泡图
│   ├── final_up_proteins_GO_MF_bubble.png # 上调蛋白MF富集气泡图
│   ├── final_up_proteins_KEGG_bubble.png  # 上调蛋白KEGG富集气泡图
│   ├── final_down_proteins_GO_BP_bubble.png  # 下调蛋白BP富集气泡图
│   ├── final_down_proteins_GO_CC_bubble.png  # 下调蛋白CC富集气泡图
│   ├── final_down_proteins_GO_MF_bubble.png  # 下调蛋白MF富集气泡图
│   └── final_down_proteins_KEGG_bubble.png   # 下调蛋白KEGG富集气泡图
├── 🕸️ networks/                           # 网络互作图
│   ├── final_up_proteins_BP_network.png   # 上调蛋白BP网络图
│   ├── final_up_proteins_KEGG_network.png # 上调蛋白KEGG网络图
│   ├── final_down_proteins_BP_network.png # 下调蛋白BP网络图
│   └── final_down_proteins_KEGG_network.png # 下调蛋白KEGG网络图
├── 📊 tables/                             # 数据表格
│   └── final_enrichment_results.xlsx      # 富集分析结果表格
└── 🐍 *.py                               # 分析脚本
```

## 🎯 图表解读

### 1. 火山图 (Volcano Plot)
- **横坐标**: log2(Fold Change) - 表示蛋白表达变化倍数
- **纵坐标**: -log10(P-value) - 表示统计显著性
- **红色点**: 上调蛋白 (141个)
- **蓝色点**: 下调蛋白 (486个)
- **灰色点**: 无显著差异蛋白

### 2. 富集分析气泡图
- **气泡大小**: 表示富集到的蛋白数量，越大表示数量越多
- **气泡颜色**: 表示统计显著性(-log10 P-value)，颜色越红表示越显著
- **横坐标**: 富集比例 (Enrichment Ratio)
- **纵坐标**: 功能条目或通路名称

### 3. 网络互作图
- **节点大小**: 表示涉及的蛋白数量
- **节点颜色**: 表示蛋白数量的多少
- **连线**: 表示功能条目或通路间的相互关系

## 🔬 分析方法

### 差异蛋白识别
- 基于原始数据中的Sig列进行识别
- Sig = 1: 上调蛋白
- Sig = -1: 下调蛋白
- Sig = 0: 无显著差异

### 功能富集分析
- **GO分析**: 基于Gene Ontology数据库
  - BP: 生物过程 (Biological Process)
  - CC: 细胞组分 (Cellular Component)
  - MF: 分子功能 (Molecular Function)
- **KEGG分析**: 基于KEGG数据库进行通路注释

### 可视化方法
- **Python**: 主要编程语言
- **matplotlib**: 图表绘制
- **seaborn**: 统计图表
- **networkx**: 网络分析和可视化

## 📋 主要富集结果

### 上调蛋白Top5功能
1. **蛋白水解** (8个蛋白)
2. **有氧呼吸** (8个蛋白)
3. **线粒体ATP合成** (8个蛋白)
4. **线粒体电子传递** (7个蛋白)
5. **先天免疫反应** (7个蛋白)

### 下调蛋白Top5功能
1. **蛋白质结合** (66个蛋白)
2. **RNA结合** (59个蛋白)
3. **ATP结合** (39个蛋白)
4. **钙离子结合** (38个蛋白)
5. **金属离子结合** (36个蛋白)

### 上调蛋白Top5通路
1. **中性粒细胞胞外陷阱形成** (13个蛋白)
2. **系统性红斑狼疮** (13个蛋白)
3. **糖尿病心肌病** (13个蛋白)
4. **癌症转录失调** (13个蛋白)
5. **志贺氏菌病** (12个蛋白)

### 下调蛋白Top5通路
1. **新冠病毒感染** (33个蛋白)
2. **补体和凝血级联反应** (30个蛋白)
3. **核糖体** (18个蛋白)
4. **运动蛋白** (16个蛋白)
5. **肌动蛋白细胞骨架调节** (15个蛋白)

## 🔍 生物学意义

### 疾病机制
- 下调蛋白数量多于上调蛋白，提示HFsnEF可能涉及蛋白表达的整体下调
- 线粒体功能相关蛋白上调，可能反映代谢补偿机制
- 免疫相关通路的变化提示炎症反应的参与

### 临床意义
- 差异蛋白可能作为疾病诊断的生物标志物
- 富集的通路可能成为治疗干预的靶点
- 为理解HFnEF和HFsnEF的分子机制提供线索

## 🛠️ 使用说明

### 查看结果
1. **完整报告**: 阅读 `最终蛋白组差异分析报告.md`
2. **图表查看**: 打开 `figures/` 和 `networks/` 文件夹中的PNG图片
3. **数据表格**: 使用Excel打开 `tables/final_enrichment_results.xlsx`

### 重新运行分析
```bash
python final_analysis.py
```

## 📞 技术支持

如有任何问题或需要进一步分析，请联系分析团队。

---

**分析完成时间**: 2025年08月02日  
**分析工具**: Python + pandas + matplotlib + networkx  
**数据来源**: Proteins_all_diff.csv
